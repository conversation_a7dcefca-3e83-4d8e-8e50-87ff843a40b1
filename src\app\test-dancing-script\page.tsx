'use client';

import { useState } from 'react';
import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { FontLoader } from '@/lib/font-loader';

export default function TestDancingScriptPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testFontLoading = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始Dancing Script字体加载测试...');
      
      // 创建PDF文档
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      addLog('📄 PDF文档创建成功');
      
      // 测试所有权重的Dancing Script字体
      const weights = [400, 500, 600, 700];
      const loadedFonts = new Map();
      
      for (const weight of weights) {
        try {
          addLog(`🔤 测试加载 Dancing Script ${weight}...`);
          
          // 使用FontLoader加载字体
          const fonts = await FontLoader.loadFonts(pdfDoc, [
            { family: 'Dancing Script', weight }
          ]);
          
          const fontKey = `Dancing Script-${weight}`;
          if (fonts.has(fontKey)) {
            const font = fonts.get(fontKey);
            loadedFonts.set(weight, font);
            addLog(`✅ Dancing Script ${weight} 加载成功: ${font?.name}`);
          } else {
            addLog(`❌ Dancing Script ${weight} 加载失败`);
          }
          
        } catch (error) {
          addLog(`❌ Dancing Script ${weight} 加载异常: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      
      // 创建测试页面
      if (loadedFonts.size > 0) {
        addLog('📝 创建测试PDF页面...');
        
        const page = pdfDoc.addPage([600, 800]);
        let yPosition = 750;
        
        // 绘制标题
        page.drawText('Dancing Script 字体测试', {
          x: 50,
          y: yPosition,
          size: 24,
          font: loadedFonts.get(400) || await pdfDoc.embedStandardFont('Helvetica')
        });
        
        yPosition -= 60;
        
        // 测试每个权重
        for (const [weight, font] of loadedFonts) {
          const testText = `Dancing Script ${weight} - 手写风格测试 Handwriting Style Test`;
          
          page.drawText(testText, {
            x: 50,
            y: yPosition,
            size: 18,
            font: font
          });
          
          yPosition -= 40;
          addLog(`✅ 绘制 Dancing Script ${weight} 文本`);
        }
        
        // 生成PDF
        const pdfBytes = await pdfDoc.save();
        addLog(`📦 PDF生成成功 (${Math.round(pdfBytes.length / 1024)} KB)`);
        
        // 下载PDF
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'dancing-script-test.pdf';
        link.click();
        URL.revokeObjectURL(url);
        
        addLog('💾 PDF下载完成');
      }
      
      addLog('🎉 Dancing Script字体测试完成！');
      
    } catch (error) {
      addLog(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('Font test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testFontFiles = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🔍 开始字体文件验证测试...');
      
      const fontPaths = [
        '/fonts/Dancing_Script/DancingScript-Regular.ttf',
        '/fonts/Dancing_Script/DancingScript-Medium.ttf',
        '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
        '/fonts/Dancing_Script/DancingScript-Bold.ttf'
      ];
      
      for (const fontPath of fontPaths) {
        try {
          addLog(`📁 检查字体文件: ${fontPath}`);
          
          const response = await fetch(fontPath);
          if (!response.ok) {
            addLog(`❌ 字体文件不存在: ${response.status} ${response.statusText}`);
            continue;
          }
          
          const arrayBuffer = await response.arrayBuffer();
          addLog(`✅ 字体文件大小: ${Math.round(arrayBuffer.byteLength / 1024)} KB`);
          
          // 验证TTF文件头
          const view = new DataView(arrayBuffer);
          const signature = view.getUint32(0, false);
          
          if (signature === 0x00010000 || signature === 0x74727565) { // TTF or TrueType
            addLog(`✅ 有效的TTF文件头: 0x${signature.toString(16).padStart(8, '0')}`);
          } else {
            addLog(`❌ 无效的TTF文件头: 0x${signature.toString(16).padStart(8, '0')}`);
          }
          
        } catch (error) {
          addLog(`❌ 检查字体文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      
      addLog('✅ 字体文件验证完成');
      
    } catch (error) {
      addLog(`❌ 验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Dancing Script 字体测试</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testFontFiles}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? '测试中...' : '验证字体文件'}
        </button>
        
        <button
          onClick={testFontLoading}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '测试字体加载和PDF生成'}
        </button>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">测试日志</h2>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
