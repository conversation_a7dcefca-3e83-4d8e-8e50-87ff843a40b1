'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { generateCertificatePDF } from '@/lib/pdf-generator';
import { TemplateManager } from '@/lib/template-manager';
import { CertificateData } from '@/types/certificate';

export default function VerifyDancingScriptPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testDancingScriptFont = async () => {
    setIsGenerating(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始Dancing Script字体验证测试...');
      
      // 获取使用Dancing Script的模板
      const templates = [
        'elegant-template-1',
        'elegant-template-2', 
        'completion-template-1'
      ];
      
      for (const templateId of templates) {
        const template = TemplateManager.getTemplateById(templateId);
        if (!template) {
          addLog(`⚠️ 模板未找到: ${templateId}`);
          continue;
        }
        
        addLog(`✅ 测试模板: ${template.displayName}`);
        
        // 检查模板是否使用Dancing Script
        const usesDancingScript = 
          template.style.fonts.name.family === 'Dancing Script' ||
          template.style.fonts.body.family === 'Dancing Script' ||
          template.style.fonts.signature.family === 'Dancing Script';
        
        if (usesDancingScript) {
          addLog(`🔤 模板使用Dancing Script字体`);
          
          // 准备测试数据
          const testData: CertificateData = {
            templateId: template.id,
            recipientName: 'Dancing Script Test',
            date: '2024-01-15',
            signature: 'Beautiful Signature',
            details: 'This certificate verifies the Dancing Script font rendering'
          };
          
          addLog('📝 生成包含Dancing Script字体的证书...');
          
          // 生成PDF
          await generateCertificatePDF(template, testData);
          
          addLog(`✅ ${template.displayName} - Dancing Script字体测试成功`);
        } else {
          addLog(`ℹ️ ${template.displayName} - 不使用Dancing Script字体`);
        }
      }
      
      addLog('🎉 所有Dancing Script字体测试完成！');
      
    } catch (error) {
      addLog(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
      addLog(`📋 错误详情: ${error instanceof Error ? error.stack : 'N/A'}`);
      console.error('Dancing Script test error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const testFontFiles = async () => {
    setIsGenerating(true);
    setLogs([]);
    
    try {
      addLog('🔍 验证Dancing Script字体文件...');
      
      const fontPaths = [
        '/fonts/Dancing_Script/static/DancingScript-Regular.ttf',
        '/fonts/Dancing_Script/static/DancingScript-Medium.ttf',
        '/fonts/Dancing_Script/static/DancingScript-SemiBold.ttf',
        '/fonts/Dancing_Script/static/DancingScript-Bold.ttf'
      ];
      
      for (const fontPath of fontPaths) {
        try {
          addLog(`🔍 检查字体文件: ${fontPath}`);
          
          const response = await fetch(fontPath);
          if (!response.ok) {
            addLog(`❌ 字体文件不存在: ${response.status}`);
            continue;
          }
          
          const arrayBuffer = await response.arrayBuffer();
          addLog(`✅ 字体文件大小: ${Math.round(arrayBuffer.byteLength / 1024)} KB`);
          
          // 验证TTF文件头
          const view = new DataView(arrayBuffer);
          const signature = view.getUint32(0, false);
          
          if (signature === 0x00010000) {
            addLog(`✅ 有效的TTF文件头: 0x${signature.toString(16).padStart(8, '0')}`);
          } else {
            addLog(`❌ 无效的TTF文件头: 0x${signature.toString(16).padStart(8, '0')}`);
          }
          
        } catch (error) {
          addLog(`❌ 检查字体文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      
      addLog('✅ Dancing Script字体文件验证完成');
      
    } catch (error) {
      addLog(`❌ 字体文件验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Dancing Script 字体验证</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* 字体文件验证 */}
        <Card>
          <CardHeader>
            <CardTitle>字体文件验证</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testFontFiles}
              disabled={isGenerating}
              className="w-full"
            >
              {isGenerating ? '正在验证...' : '验证字体文件'}
            </Button>
            
            <div className="text-sm text-gray-600">
              <p>验证内容：</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>检查TTF文件是否存在</li>
                <li>验证文件大小合理性</li>
                <li>检查TTF文件头（0x00010000）</li>
                <li>确认所有权重可用</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* PDF生成测试 */}
        <Card>
          <CardHeader>
            <CardTitle>PDF生成测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testDancingScriptFont}
              disabled={isGenerating}
              className="w-full"
            >
              {isGenerating ? '正在测试...' : '测试PDF生成'}
            </Button>
            
            <div className="text-sm text-gray-600">
              <p>测试内容：</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>使用Dancing Script的模板</li>
                <li>生成包含手写字体的PDF</li>
                <li>验证无缓冲区错误</li>
                <li>确认字体正确嵌入</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-4 mb-6">
        <Button onClick={clearLogs} variant="outline">
          清除日志
        </Button>
      </div>

      {/* 字体信息 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Dancing Script 字体信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="font-semibold">可用权重</div>
              <div>Regular (400)</div>
              <div>Medium (500)</div>
              <div>SemiBold (600)</div>
              <div>Bold (700)</div>
            </div>
            <div className="space-y-2">
              <div className="font-semibold">文件路径</div>
              <div className="text-xs text-gray-600">
                /fonts/Dancing_Script/static/
              </div>
              <div className="font-semibold">备用字体</div>
              <div>Times Roman Italic</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 日志输出 */}
      <Card>
        <CardHeader>
          <CardTitle>验证日志</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500">选择一个验证测试开始...</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="font-mono text-sm">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
