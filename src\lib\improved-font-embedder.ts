/**
 * 改进的PDF字体嵌入器
 * 解决Adobe Acrobat字体提取问题，确保字体正确嵌入和显示
 */

import { PDFDocument, PDFFont, StandardFonts } from 'pdf-lib';
import { GoogleFontsManager } from './google-fonts-manager';

export interface ImprovedEmbedOptions {
  subset?: boolean;
  embedStandardFonts?: boolean;
  customSubsetText?: string;
  fontName?: string;
}

export interface ImprovedFontEmbedResult {
  font: PDFFont;
  family: string;
  weight: number;
  isCustom: boolean;
  fallbackUsed: boolean;
  embedMethod: 'custom' | 'standard' | 'fallback';
  fontName: string;
}

/**
 * 改进的PDF字体嵌入器类
 * 专门解决Adobe Acrobat兼容性问题
 */
export class ImprovedFontEmbedder {
  private pdfDoc: PDFDocument;
  private googleFontsManager: GoogleFontsManager;
  private embeddedFonts = new Map<string, ImprovedFontEmbedResult>();
  private usedCharacters = new Set<string>();

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
    this.googleFontsManager = GoogleFontsManager.getInstance();
  }

  /**
   * 收集文档中使用的字符
   */
  collectUsedCharacters(texts: string[]): void {
    texts.forEach(text => {
      if (text) {
        for (const char of text) {
          this.usedCharacters.add(char);
        }
      }
    });
    
    // 添加常用字符确保基本功能
    const basicChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?-()[]{}:;"\'@#$%^&*+=<>/\\|`~_ ';
    for (const char of basicChars) {
      this.usedCharacters.add(char);
    }
    
    console.log(`📝 Collected ${this.usedCharacters.size} unique characters for font subsetting`);
  }

  /**
   * 嵌入字体到PDF - 改进版本
   */
  async embedFont(family: string, weight: number = 400): Promise<ImprovedFontEmbedResult> {
    const cacheKey = `${family}-${weight}`;

    // 检查是否已嵌入
    if (this.embeddedFonts.has(cacheKey)) {
      console.log(`✅ Font already embedded: ${cacheKey}`);
      return this.embeddedFonts.get(cacheKey)!;
    }

    console.log(`🔤 Embedding font with improved method: ${family} ${weight}`);

    try {
      // 1. 尝试嵌入优化的自定义字体
      const customResult = await this.embedOptimizedCustomFont(family, weight);
      if (customResult) {
        this.embeddedFonts.set(cacheKey, customResult);
        return customResult;
      }

      // 2. 尝试嵌入TTF格式字体
      const ttfResult = await this.embedTTFFont(family, weight);
      if (ttfResult) {
        this.embeddedFonts.set(cacheKey, ttfResult);
        return ttfResult;
      }

      // 3. 使用标准字体作为后备
      const fallbackResult = await this.embedStandardFont(family, weight);
      this.embeddedFonts.set(cacheKey, fallbackResult);
      return fallbackResult;

    } catch (error) {
      console.error(`❌ Error embedding font ${family} ${weight}:`, error);
      
      // 最终后备：Helvetica
      const helveticaResult = await this.embedStandardFont('Helvetica', weight);
      this.embeddedFonts.set(cacheKey, helveticaResult);
      return helveticaResult;
    }
  }

  /**
   * 嵌入优化的自定义字体
   */
  private async embedOptimizedCustomFont(family: string, weight: number): Promise<ImprovedFontEmbedResult | null> {
    try {
      // 从Google Fonts获取字体数据
      const fontData = await this.googleFontsManager.getFontDataForPDF(family, weight);
      if (!fontData) {
        console.warn(`⚠️ No font data available for ${family} ${weight}`);
        return null;
      }

      console.log(`📦 Embedding optimized custom font: ${family} ${weight} (${fontData.byteLength} bytes)`);

      // 生成Adobe兼容的字体名称
      const fontName = this.generateAdobeCompatibleFontName(family, weight);
      
      // 创建子集文本
      const subsetText = Array.from(this.usedCharacters).join('');
      
      // 优化的嵌入策略 - 专门针对Adobe Acrobat
      const strategies: ImprovedEmbedOptions[] = [
        { 
          subset: true, 
          customSubsetText: subsetText,
          fontName: fontName
        },
        { 
          subset: false,
          fontName: fontName
        },
        { 
          subset: true,
          fontName: fontName
        },
        {} // 默认选项
      ];

      for (const options of strategies) {
        try {
          console.log(`🔧 Trying embed strategy: ${JSON.stringify(options)}`);
          
          const font = await this.pdfDoc.embedFont(fontData, {
            subset: options.subset,
            customName: options.fontName
          });
          
          console.log(`✅ Optimized custom font embedded successfully: ${family} ${weight}`);
          console.log(`   Font name: ${font.name}`);
          console.log(`   Adobe compatible name: ${fontName}`);
          console.log(`   Subset: ${options.subset ? 'Yes' : 'No'}`);

          return {
            font,
            family,
            weight,
            isCustom: true,
            fallbackUsed: false,
            embedMethod: 'custom',
            fontName: font.name
          };

        } catch (embedError) {
          console.warn(`⚠️ Embed strategy failed for ${family} ${weight}:`, embedError);
          continue;
        }
      }

      console.error(`❌ All optimized embed strategies failed for ${family} ${weight}`);
      return null;

    } catch (error) {
      console.error(`❌ Error in embedOptimizedCustomFont for ${family} ${weight}:`, error);
      return null;
    }
  }

  /**
   * 尝试嵌入TTF格式字体
   */
  private async embedTTFFont(family: string, weight: number): Promise<ImprovedFontEmbedResult | null> {
    try {
      console.log(`🔍 Attempting to load TTF format for ${family} ${weight}`);
      
      // 尝试获取TTF格式的字体URL
      const ttfUrl = await this.getTTFFontUrl(family, weight);
      if (!ttfUrl) {
        return null;
      }

      // 下载TTF字体数据
      const response = await fetch(ttfUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      if (!response.ok) {
        console.warn(`Failed to fetch TTF font: ${response.status}`);
        return null;
      }

      const fontData = await response.arrayBuffer();
      console.log(`📦 TTF font data loaded: ${fontData.byteLength} bytes`);

      // 嵌入TTF字体
      const fontName = this.generateAdobeCompatibleFontName(family, weight);
      const font = await this.pdfDoc.embedFont(fontData, {
        subset: true,
        customName: fontName
      });

      console.log(`✅ TTF font embedded successfully: ${family} ${weight}`);
      console.log(`   Font name: ${font.name}`);

      return {
        font,
        family,
        weight,
        isCustom: true,
        fallbackUsed: false,
        embedMethod: 'custom',
        fontName: font.name
      };

    } catch (error) {
      console.warn(`⚠️ TTF font embedding failed for ${family} ${weight}:`, error);
      return null;
    }
  }

  /**
   * 获取TTF格式的字体URL
   */
  private async getTTFFontUrl(family: string, weight: number): Promise<string | null> {
    try {
      // 使用特定的User-Agent来获取TTF格式
      const cssUrl = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(family)}:wght@${weight}&display=swap`;
      
      const response = await fetch(cssUrl, {
        headers: {
          // 使用旧版浏览器User-Agent来获取TTF格式
          'User-Agent': 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)'
        }
      });

      if (!response.ok) {
        return null;
      }

      const cssText = await response.text();
      
      // 查找TTF或OTF格式的URL
      const urlMatch = cssText.match(/url\(([^)]+\.(?:ttf|otf))\)/i);
      if (urlMatch && urlMatch[1]) {
        const fontUrl = urlMatch[1].replace(/['"]/g, '');
        console.log(`🔗 TTF font URL found: ${fontUrl}`);
        return fontUrl;
      }

      return null;
    } catch (error) {
      console.warn(`Failed to get TTF font URL for ${family} ${weight}:`, error);
      return null;
    }
  }

  /**
   * 生成Adobe兼容的字体名称
   */
  private generateAdobeCompatibleFontName(family: string, weight: number): string {
    // 移除特殊字符，使用Adobe兼容的命名规则
    const cleanFamily = family.replace(/[^a-zA-Z0-9]/g, '');
    const weightSuffix = weight === 400 ? 'Regular' : 
                        weight === 700 ? 'Bold' : 
                        weight === 600 ? 'SemiBold' :
                        weight === 300 ? 'Light' : 
                        `W${weight}`;
    
    return `${cleanFamily}-${weightSuffix}`;
  }

  /**
   * 嵌入标准字体
   */
  private async embedStandardFont(family: string, weight: number): Promise<ImprovedFontEmbedResult> {
    console.log(`🔄 Using standard font for ${family} ${weight}`);

    const fallbackMapping = this.getFallbackFont(family, weight >= 600);
    
    try {
      const font = await this.pdfDoc.embedFont(fallbackMapping.standardFont);
      
      console.log(`✅ Standard font embedded: ${fallbackMapping.family}`);

      return {
        font,
        family: fallbackMapping.family,
        weight: weight >= 600 ? 700 : 400,
        isCustom: false,
        fallbackUsed: true,
        embedMethod: 'standard',
        fontName: font.name
      };

    } catch (error) {
      console.error(`❌ Error embedding standard font:`, error);
      throw error;
    }
  }

  /**
   * 获取后备字体映射
   */
  private getFallbackFont(family: string, isBold: boolean): { standardFont: any, family: string } {
    const familyLower = family.toLowerCase();

    // 手写体/装饰体 -> Times (最接近的衬线字体)
    if (familyLower.includes('dancing') || 
        familyLower.includes('script') ||
        familyLower.includes('vibes') ||
        familyLower.includes('cursive')) {
      return { 
        standardFont: isBold ? StandardFonts.TimesRomanBold : StandardFonts.TimesRoman,
        family: isBold ? 'Times-Bold' : 'Times-Roman'
      };
    }

    // 衬线体 -> Times
    if (familyLower.includes('serif') || 
        familyLower.includes('playfair') ||
        familyLower.includes('crimson') ||
        familyLower.includes('times')) {
      return { 
        standardFont: isBold ? StandardFonts.TimesRomanBold : StandardFonts.TimesRoman,
        family: isBold ? 'Times-Bold' : 'Times-Roman'
      };
    }

    // 等宽字体 -> Courier
    if (familyLower.includes('mono') || 
        familyLower.includes('courier') ||
        familyLower.includes('code')) {
      return { 
        standardFont: isBold ? StandardFonts.CourierBold : StandardFonts.Courier,
        family: isBold ? 'Courier-Bold' : 'Courier'
      };
    }

    // 默认：无衬线字体 -> Helvetica
    return { 
      standardFont: isBold ? StandardFonts.HelveticaBold : StandardFonts.Helvetica,
      family: isBold ? 'Helvetica-Bold' : 'Helvetica'
    };
  }

  /**
   * 获取已嵌入的字体
   */
  getEmbeddedFont(family: string, weight: number = 400): ImprovedFontEmbedResult | null {
    const cacheKey = `${family}-${weight}`;
    return this.embeddedFonts.get(cacheKey) || null;
  }

  /**
   * 获取嵌入统计信息
   */
  getEmbedStats(): { total: number; custom: number; standard: number; fallback: number } {
    const fonts = Array.from(this.embeddedFonts.values());
    return {
      total: fonts.length,
      custom: fonts.filter(f => f.embedMethod === 'custom').length,
      standard: fonts.filter(f => f.embedMethod === 'standard').length,
      fallback: fonts.filter(f => f.fallbackUsed).length
    };
  }
}
