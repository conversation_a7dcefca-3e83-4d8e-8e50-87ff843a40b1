'use client';

import { PDFDocument, PDFFont, StandardFonts } from 'pdf-lib';
import { FONT_MAPPING, getPdfFontKey } from './fonts';

/**
 * 字体加载器类
 * 负责加载本地字体文件，确保PDF中字体正确嵌入
 * 包含系统字体备用方案以确保稳定性
 */
export class FontLoader {
  private static fontCache: Map<string, ArrayBuffer> = new Map();
  private static fontValidationCache: Map<string, boolean> = new Map();

  /**
   * 本地字体文件映射（仅包含可靠的TTF字体）
   */
  private static LOCAL_FONTS: Record<string, Record<number, string>> = {
    'Open Sans': {
      400: '/fonts/open-sans/open-sans-400.ttf',
      700: '/fonts/open-sans/open-sans-700.ttf'
    },
    'Roboto': {
      400: '/fonts/roboto/roboto-400.ttf',
      700: '/fonts/roboto/roboto-700.ttf'
    },
    'Dancing Script': {
      400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
      500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
      600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
      700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
    }
  };

  /**
   * 系统字体备用映射
   */
  private static FALLBACK_FONTS: Record<string, StandardFonts> = {
    'Open Sans': StandardFonts.Helvetica,
    'Roboto': StandardFonts.Helvetica,
    'Dancing Script': StandardFonts.TimesRomanItalic, // 手写风格字体备用到斜体
    // 其他字体都备用到Helvetica
    'Great Vibes': StandardFonts.TimesRomanItalic,
    'Playfair Display': StandardFonts.TimesRoman,
    'Inter': StandardFonts.Helvetica,
    'Source Sans Pro': StandardFonts.Helvetica,
    'Lato': StandardFonts.Helvetica,
    'Montserrat': StandardFonts.Helvetica
  };

  /**
   * 验证字体文件是否有效
   */
  private static async validateFontFile(fontBytes: ArrayBuffer): Promise<boolean> {
    try {
      // 检查文件大小
      if (fontBytes.byteLength < 1000) {
        console.warn('Font file too small, likely corrupted');
        return false;
      }

      // 检查TTF文件头
      const view = new DataView(fontBytes);
      const signature = view.getUint32(0, false);

      // TTF文件应该以0x00010000或'OTTO'开头
      if (signature !== 0x00010000 && signature !== 0x4F54544F) {
        console.warn('Invalid font file signature');
        return false;
      }

      return true;
    } catch (error) {
      console.warn('Font validation failed:', error);
      return false;
    }
  }

  /**
   * 从本地文件加载字体（带验证）
   */
  private static async loadLocalFont(fontFamily: string, weight: number = 400): Promise<ArrayBuffer | null> {
    const cacheKey = `${fontFamily}-${weight}`;

    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      console.log(`✅ Font found in cache: ${cacheKey}`);
      return this.fontCache.get(cacheKey)!;
    }

    try {
      // 获取本地字体文件路径
      const fontPath = this.LOCAL_FONTS[fontFamily]?.[weight];
      if (!fontPath) {
        console.warn(`No local font file found for ${fontFamily} weight ${weight}`);
        return null;
      }

      console.log(`🔍 Loading local font: ${cacheKey} from ${fontPath}`);

      // 加载本地字体文件
      const fontResponse = await fetch(fontPath);

      if (!fontResponse.ok) {
        console.warn(`Failed to fetch local font ${cacheKey}: ${fontResponse.status}`);
        return null;
      }

      const fontBytes = await fontResponse.arrayBuffer();

      // 验证字体文件
      const isValid = await this.validateFontFile(fontBytes);
      if (!isValid) {
        console.warn(`Font file validation failed for ${cacheKey}`);
        return null;
      }

      console.log(`✅ Local font loaded and validated: ${cacheKey} (${fontBytes.byteLength} bytes)`);

      // 缓存字体数据
      this.fontCache.set(cacheKey, fontBytes);
      this.fontValidationCache.set(cacheKey, true);

      return fontBytes;
    } catch (error) {
      console.warn(`Failed to load local font ${cacheKey}:`, error);
      this.fontValidationCache.set(cacheKey, false);
      return null;
    }
  }

  /**
   * 从Google Fonts CSS API动态获取字体URL并加载（备用方案）
   */
  private static async loadGoogleFont(fontFamily: string, weight: number = 400): Promise<ArrayBuffer | null> {
    const normalizedFontFamily = getPdfFontKey(fontFamily);
    const cacheKey = `${normalizedFontFamily}-${weight}-google`;

    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      console.log(`✅ Google font found in cache: ${cacheKey}`);
      return this.fontCache.get(cacheKey)!;
    }

    try {
      console.log(`🔍 Loading Google font as fallback: ${cacheKey}`);

      // 1. 获取Google Fonts CSS
      const cssUrl = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(normalizedFontFamily)}:wght@${weight}&display=swap`;

      const cssResponse = await fetch(cssUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      if (!cssResponse.ok) {
        console.warn(`Failed to fetch CSS for ${cacheKey}: ${cssResponse.status}`);
        return null;
      }

      const cssText = await cssResponse.text();

      // 2. 从CSS中提取字体URL
      const urlMatch = cssText.match(/url\(([^)]+)\)/);
      if (!urlMatch || !urlMatch[1]) {
        console.warn(`Could not extract font URL from CSS for ${cacheKey}`);
        return null;
      }

      const fontUrl = urlMatch[1].replace(/['"]/g, '');
      console.log(`🔗 Extracted font URL: ${fontUrl.substring(0, 80)}...`);

      // 3. 下载字体文件
      const fontResponse = await fetch(fontUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      if (!fontResponse.ok) {
        console.warn(`Failed to fetch Google font ${cacheKey}: ${fontResponse.status}`);
        return null;
      }

      const fontBytes = await fontResponse.arrayBuffer();
      console.log(`✅ Google font loaded successfully: ${cacheKey} (${fontBytes.byteLength} bytes)`);

      // 缓存字体数据
      this.fontCache.set(cacheKey, fontBytes);

      return fontBytes;
    } catch (error) {
      console.warn(`Failed to load Google font ${cacheKey}:`, error);
      return null;
    }
  }

  /**
   * 批量加载字体（优先本地字体，失败时使用系统字体备用）
   */
  static async loadFonts(
    pdfDoc: PDFDocument,
    fontConfigs: Array<{ family: string; weight?: number }>
  ): Promise<Map<string, PDFFont>> {
    const fonts = new Map<string, PDFFont>();

    for (const { family, weight = 400 } of fontConfigs) {
      let font: PDFFont | null = null;

      try {
        // 1. 首先尝试加载本地字体
        const fontBytes = await this.loadLocalFont(family, weight);

        if (fontBytes) {
          try {
            font = await pdfDoc.embedFont(fontBytes);
            console.log(`✅ Successfully embedded local font: ${family}-${weight}`);
          } catch (embedError) {
            console.warn(`❌ Failed to embed local font ${family}-${weight}:`, embedError);
            font = null;
          }
        }

        // 2. 如果本地字体失败，使用系统字体备用
        if (!font) {
          const fallbackFont = this.FALLBACK_FONTS[family];
          if (fallbackFont) {
            try {
              font = pdfDoc.embedStandardFont(fallbackFont);
              console.log(`⚠️ Using fallback system font for ${family}: ${fallbackFont}`);
            } catch (fallbackError) {
              console.warn(`❌ Failed to embed fallback font for ${family}:`, fallbackError);
            }
          }
        }

        // 3. 最后的备用方案：使用Helvetica
        if (!font) {
          try {
            font = pdfDoc.embedStandardFont(StandardFonts.Helvetica);
            console.log(`⚠️ Using Helvetica as final fallback for ${family}`);
          } catch (finalError) {
            console.error(`❌ Failed to embed final fallback font:`, finalError);
            continue; // 跳过这个字体
          }
        }

        if (font) {
          fonts.set(`${family}-${weight}`, font);
          fonts.set(family, font); // 也用简单名称作为键
        }

      } catch (error) {
        console.error(`❌ Unexpected error loading font ${family}-${weight}:`, error);

        // 紧急备用：直接使用Helvetica
        try {
          const emergencyFont = pdfDoc.embedStandardFont(StandardFonts.Helvetica);
          fonts.set(`${family}-${weight}`, emergencyFont);
          fonts.set(family, emergencyFont);
          console.log(`🚨 Emergency fallback: Using Helvetica for ${family}`);
        } catch (emergencyError) {
          console.error(`❌ Emergency fallback failed:`, emergencyError);
        }
      }
    }

    return fonts;
  }

  /**
   * 清除字体缓存
   */
  static clearCache(): void {
    this.fontCache.clear();
  }

  /**
   * 获取缓存大小
   */
  static getCacheSize(): number {
    return this.fontCache.size;
  }
}
