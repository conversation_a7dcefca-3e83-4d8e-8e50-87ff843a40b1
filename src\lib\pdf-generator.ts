'use client';

import { PDFDocument, PDFPage, PDFFont, StandardFonts, rgb } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { downloadFile } from '@/lib/utils';
import { measurePerformance } from '@/components/common/PerformanceMonitor';
import { FontLoader } from '@/lib/font-loader';
import { getPdfFontKey } from '@/lib/fonts';

/**
 * PDF生成器类
 * 负责将证书模板和数据转换为高质量的PDF文件
 */
export class PDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc: PDFDocument | null = null;
  private page: PDFPage | null = null;
  private fonts: { [key: string]: PDFFont } = {};

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
  }

  /**
   * 生成PDF并返回字节数组
   */
  async generate(): Promise<Uint8Array> {
    const startTime = performance.now();
    measurePerformance.mark('pdf-generation-start');

    try {
      // 创建PDF文档
      this.pdfDoc = await PDFDocument.create();

      // 注册fontkit以支持自定义字体
      this.pdfDoc.registerFontkit(fontkit);

      // 设置文档元数据
      this.setDocumentMetadata();
      
      // 根据模板方向添加页面
      const pageSize = this.getPageSize();
      this.page = this.pdfDoc.addPage(pageSize);
      
      // 加载字体
      await this.loadFonts();
      
      // 绘制证书内容
      await this.drawCertificate();
      
      // 返回PDF字节数组
      const pdfBytes = await this.pdfDoc.save();

      // 性能监控
      measurePerformance.mark('pdf-generation-end');
      const endTime = performance.now();
      measurePerformance.measurePDFGeneration(startTime, endTime);
      measurePerformance.measure('pdf-generation-total', 'pdf-generation-start', 'pdf-generation-end');

      return pdfBytes;
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 生成PDF并自动下载
   */
  async generateAndDownload(): Promise<void> {
    const pdfBytes = await this.generate();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const filename = `certificate-${this.data.recipientName.replace(/[^a-zA-Z0-9]/g, '_')}-${Date.now()}.pdf`;
    downloadFile(blob, filename);
  }



  /**
   * 设置PDF文档元数据
   */
  private setDocumentMetadata(): void {
    if (!this.pdfDoc) return;

    const certificateTitle = 'Certificate';
    this.pdfDoc.setTitle(`${certificateTitle} - ${this.data.recipientName}`);
    this.pdfDoc.setSubject(certificateTitle);
    this.pdfDoc.setAuthor('Certificate Maker');
    this.pdfDoc.setCreator('Certificate Maker - https://certificate-maker.com');
    this.pdfDoc.setProducer('PDF-lib');
    this.pdfDoc.setCreationDate(new Date());
    this.pdfDoc.setModificationDate(new Date());
  }

  /**
   * 根据模板方向获取页面尺寸
   */
  private getPageSize(): [number, number] {
    // A4尺寸: 595.28 x 841.89 points
    const A4_WIDTH = 595.28;
    const A4_HEIGHT = 841.89;

    if (this.template.orientation === 'landscape') {
      // 横向：宽度 > 高度
      return [A4_HEIGHT, A4_WIDTH]; // 841.89 x 595.28
    } else {
      // 竖向：高度 > 宽度
      return [A4_WIDTH, A4_HEIGHT]; // 595.28 x 841.89
    }
  }

  /**
   * 加载所需字体
   */
  private async loadFonts(): Promise<void> {
    if (!this.pdfDoc) return;

    try {
      // 加载标准字体
      this.fonts.helvetica = this.pdfDoc.embedStandardFont(StandardFonts.Helvetica);
      this.fonts.helveticaBold = this.pdfDoc.embedStandardFont(StandardFonts.HelveticaBold);
      this.fonts.timesRoman = this.pdfDoc.embedStandardFont(StandardFonts.TimesRoman);
      this.fonts.timesRomanBold = this.pdfDoc.embedStandardFont(StandardFonts.TimesRomanBold);
      this.fonts.courier = this.pdfDoc.embedStandardFont(StandardFonts.Courier);
      this.fonts.courierBold = this.pdfDoc.embedStandardFont(StandardFonts.CourierBold);

      // 尝试加载自定义字体，失败时使用标准字体作为后备
      try {
        const customFonts = await FontLoader.loadFonts(this.pdfDoc, [
          { family: 'Dancing Script', weight: 400 },
          { family: 'Playfair Display', weight: 400 },
          { family: 'Playfair Display', weight: 600 },
          { family: 'Playfair Display', weight: 700 },
          { family: 'Inter', weight: 400 },
          { family: 'Inter', weight: 600 },
          { family: 'Crimson Text', weight: 400 },
          { family: 'Crimson Text', weight: 600 },
          { family: 'Crimson Text', weight: 700 },
          { family: 'Source Sans 3', weight: 400 },
          { family: 'Source Sans 3', weight: 600 },
          { family: 'Source Sans 3', weight: 700 },
          { family: 'Great Vibes', weight: 400 }
        ]);

        // 合并自定义字体到字体映射
        customFonts.forEach((font, key) => {
          this.fonts[key] = font;
        });

        console.log('Successfully loaded custom fonts:', Array.from(customFonts.keys()));
      } catch (error) {
        console.warn('Failed to load custom fonts, using fallbacks:', error);
      }

      // 设置后备字体映射
      if (!this.fonts['Dancing Script']) {
        this.fonts['Dancing Script'] = this.fonts.timesRoman;
      }
      if (!this.fonts['Playfair Display']) {
        this.fonts['Playfair Display'] = this.fonts.timesRomanBold;
      }
      if (!this.fonts['Inter']) {
        this.fonts['Inter'] = this.fonts.helvetica;
      }
      if (!this.fonts['Crimson Text']) {
        this.fonts['Crimson Text'] = this.fonts.timesRoman;
      }
      if (!this.fonts['Source Sans Pro']) {
        this.fonts['Source Sans Pro'] = this.fonts.helvetica;
      }
      if (!this.fonts['Great Vibes']) {
        this.fonts['Great Vibes'] = this.fonts.timesRoman;
      }
    } catch (error) {
      console.error('Font loading error:', error);
      throw new Error('Failed to load fonts');
    }
  }

  /**
   * 绘制证书内容
   */
  private async drawCertificate(): Promise<void> {
    if (!this.page) return;

    // 绘制背景和边框
    await this.drawBackground();

    // 绘制装饰元素
    this.drawDecorations();

    // 绘制文本内容
    this.drawTexts();
  }

  /**
   * 绘制背景和边框
   */
  private async drawBackground(): Promise<void> {
    if (!this.page || !this.pdfDoc) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    // 如果有背景图片，绘制背景图片
    if (this.template.backgroundImage) {
      try {
        await this.drawBackgroundImage();
      } catch (error) {
        console.warn('Failed to load background image, falling back to color background:', error);
        // 如果背景图片加载失败，使用颜色背景
        this.drawColorBackground();
      }
    } else {
      // 绘制颜色背景
      this.drawColorBackground();
    }

    // 对于有背景图片的模板，不绘制边框（因为边框已经在图片中）
    if (!this.template.backgroundImage) {
      // 绘制外边框
      const borderWidth = this.getBorderWidth();
      this.page.drawRectangle({
        x: 20,
        y: 20,
        width: width - 40,
        height: height - 40,
        borderColor: this.hexToRgb(colors.primary),
        borderWidth: borderWidth,
      });

      // 绘制内边框
      this.page.drawRectangle({
        x: 30,
        y: 30,
        width: width - 60,
        height: height - 60,
        borderColor: this.hexToRgb(colors.primary),
        borderWidth: 1,
        opacity: 0.3,
      });
    }
  }

  /**
   * 绘制颜色背景
   */
  private drawColorBackground(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    this.page.drawRectangle({
      x: 0,
      y: 0,
      width,
      height,
      color: this.hexToRgb(colors.background),
    });
  }

  /**
   * 绘制背景图片
   */
  private async drawBackgroundImage(): Promise<void> {
    if (!this.page || !this.pdfDoc || !this.template.backgroundImage) return;

    const { width, height } = this.page.getSize();

    try {
      // 获取图片数据
      let imageUrl = this.template.backgroundImage;

      // 如果是相对路径，转换为完整URL
      if (imageUrl.startsWith('/')) {
        imageUrl = `${window.location.origin}${imageUrl}`;
      }

      console.log('Loading background image:', imageUrl);
      const response = await fetch(imageUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      const imageBytes = await response.arrayBuffer();

      // 根据文件扩展名确定图片类型
      let image;
      if (imageUrl.toLowerCase().endsWith('.png')) {
        image = await this.pdfDoc.embedPng(imageBytes);
      } else if (imageUrl.toLowerCase().endsWith('.jpg') || imageUrl.toLowerCase().endsWith('.jpeg')) {
        image = await this.pdfDoc.embedJpg(imageBytes);
      } else {
        throw new Error(`Unsupported image format: ${imageUrl}`);
      }

      // 获取图片的原始尺寸
      const imageDims = image.scale(1);

      // 计算缩放比例以适应页面
      const scaleX = width / imageDims.width;
      const scaleY = height / imageDims.height;
      const scale = Math.min(scaleX, scaleY);

      // 计算居中位置
      const scaledWidth = imageDims.width * scale;
      const scaledHeight = imageDims.height * scale;
      const x = (width - scaledWidth) / 2;
      const y = (height - scaledHeight) / 2;

      // 绘制图片，保持宽高比并居中
      this.page.drawImage(image, {
        x,
        y,
        width: scaledWidth,
        height: scaledHeight,
      });

      console.log('Background image drawn successfully');
    } catch (error) {
      console.error('Error drawing background image:', error);
      throw error;
    }
  }

  /**
   * 绘制装饰元素
   */
  private drawDecorations(): void {
    if (!this.page) return;

    // 根据用户要求，移除所有装饰线条和装饰元素
    // 保留空方法以维持代码结构
    console.log('🎨 Decorations disabled by user request');
  }

  /**
   * 绘制文本内容
   */
  private drawTexts(): void {
    if (!this.page) return;

    // 不绘制标题 - 根据用户要求移除
    // this.drawTitle();

    // 绘制收件人姓名
    this.drawRecipientName();

    // 绘制详细信息
    this.drawDetails();

    // 绘制日期和签名
    this.drawDateAndSignature();
  }



  /**
   * 绘制收件人姓名
   */
  private drawRecipientName(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const nameLayout = this.template.layout.name;
    const nameFont = this.getFont(nameLayout.fontFamily, nameLayout.fontWeight || 600);
    const nameSize = nameLayout.fontSize;
    const nameColor = this.hexToRgb(nameLayout.color);

    // 使用模板定义的位置和对齐方式
    let x = nameLayout.x;
    if (nameLayout.align === 'center') {
      const nameWidth = nameFont.widthOfTextAtSize(this.data.recipientName, nameSize);
      x = nameLayout.x - nameWidth / 2;
    } else if (nameLayout.align === 'right') {
      const nameWidth = nameFont.widthOfTextAtSize(this.data.recipientName, nameSize);
      x = nameLayout.x - nameWidth;
    }

    this.page.drawText(this.data.recipientName, {
      x,
      y: height - nameLayout.y,
      size: nameSize,
      font: nameFont,
      color: nameColor,
    });
  }

  /**
   * 绘制详细信息
   */
  private drawDetails(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const detailsLayout = this.template.layout.details;
    const bodyFont = this.getFont(detailsLayout.fontFamily, detailsLayout.fontWeight || 400);
    const bodySize = detailsLayout.fontSize;
    const bodyColor = this.hexToRgb(detailsLayout.color);

    // 分割长文本为多行
    const maxWidth = detailsLayout.width;
    const lines = this.wrapText(this.data.details, bodyFont, bodySize, maxWidth);

    const lineHeight = bodySize * 1.5;
    const startY = height - detailsLayout.y;

    lines.forEach((line, index) => {
      let x = detailsLayout.x;
      if (detailsLayout.align === 'center') {
        const lineWidth = bodyFont.widthOfTextAtSize(line, bodySize);
        x = detailsLayout.x - lineWidth / 2;
      } else if (detailsLayout.align === 'right') {
        const lineWidth = bodyFont.widthOfTextAtSize(line, bodySize);
        x = detailsLayout.x - lineWidth;
      }

      this.page!.drawText(line, {
        x,
        y: startY - (index * lineHeight),
        size: bodySize,
        font: bodyFont,
        color: bodyColor,
      });
    });
  }

  /**
   * 绘制日期和签名
   */
  private drawDateAndSignature(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const dateLayout = this.template.layout.date;
    const signatureLayout = this.template.layout.signature;

    // 确保日期和签名数据存在
    if (!this.data.date || !this.data.signature) {
      console.warn('Missing date or signature data:', {
        date: this.data.date,
        signature: this.data.signature
      });
      return;
    }

    // 绘制日期
    if (this.data.date && this.data.date.trim()) {
      const dateFont = this.getFont(dateLayout.fontFamily, dateLayout.fontWeight || 400);
      const dateSize = dateLayout.fontSize;
      const dateColor = this.hexToRgb(dateLayout.color);

      let dateX = dateLayout.x;
      if (dateLayout.align === 'center') {
        const dateWidth = dateFont.widthOfTextAtSize(this.data.date, dateSize);
        dateX = dateLayout.x - dateWidth / 2;
      } else if (dateLayout.align === 'right') {
        const dateWidth = dateFont.widthOfTextAtSize(this.data.date, dateSize);
        dateX = dateLayout.x - dateWidth;
      }

      this.page.drawText(this.data.date, {
        x: dateX,
        y: height - dateLayout.y,
        size: dateSize,
        font: dateFont,
        color: dateColor,
      });
    }

    // 绘制签名
    if (this.data.signature && this.data.signature.trim()) {
      const signatureFont = this.getFont(signatureLayout.fontFamily, signatureLayout.fontWeight || 400);
      const signatureSize = signatureLayout.fontSize;
      const signatureColor = this.hexToRgb(signatureLayout.color);

      let signatureX = signatureLayout.x;
      if (signatureLayout.align === 'center') {
        const signatureWidth = signatureFont.widthOfTextAtSize(this.data.signature, signatureSize);
        signatureX = signatureLayout.x - signatureWidth / 2;
      } else if (signatureLayout.align === 'right') {
        const signatureWidth = signatureFont.widthOfTextAtSize(this.data.signature, signatureSize);
        signatureX = signatureLayout.x - signatureWidth;
      }

      this.page.drawText(this.data.signature, {
        x: signatureX,
        y: height - signatureLayout.y,
        size: signatureSize,
        font: signatureFont,
        color: signatureColor,
      });
    }
  }

  /**
   * 获取字体对象 - 改进版本，更好地匹配自定义字体
   */
  private getFont(family: string, weight: string | number): PDFFont {
    const numericWeight = typeof weight === 'string' ?
      (weight === 'bold' ? 700 : 400) : weight;
    const isBold = numericWeight >= 600;

    // 首先尝试精确匹配（包含权重）
    const exactKey = `${family}-${numericWeight}`;
    if (this.fonts[exactKey]) {
      console.log(`Found exact font match: ${exactKey}`);
      return this.fonts[exactKey];
    }

    // 尝试简单名称匹配
    if (this.fonts[family]) {
      console.log(`Found simple font match: ${family}`);
      return this.fonts[family];
    }

    // 尝试权重变体匹配
    const weightVariants = [400, 600, 700];
    for (const variant of weightVariants) {
      const variantKey = `${family}-${variant}`;
      if (this.fonts[variantKey]) {
        console.log(`Found font variant match: ${variantKey}`);
        return this.fonts[variantKey];
      }
    }

    // 特殊字体映射 - 优先使用自定义字体
    const familyLower = family.toLowerCase();
    if (familyLower.includes('dancing script')) {
      return this.fonts['Dancing Script-400'] || this.fonts['Dancing Script'] || this.fonts.timesRoman;
    } else if (familyLower.includes('playfair')) {
      const playfairWeight = isBold ? 600 : 400;
      return this.fonts[`Playfair Display-${playfairWeight}`] || this.fonts['Playfair Display'] ||
             (isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman);
    } else if (familyLower.includes('inter')) {
      const interWeight = isBold ? 600 : 400;
      return this.fonts[`Inter-${interWeight}`] || this.fonts['Inter'] ||
             (isBold ? this.fonts.helveticaBold : this.fonts.helvetica);
    } else if (familyLower.includes('crimson')) {
      const crimsonWeight = isBold ? 600 : 400;
      return this.fonts[`Crimson Text-${crimsonWeight}`] || this.fonts['Crimson Text'] ||
             (isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman);
    } else if (familyLower.includes('source sans')) {
      const sourceWeight = isBold ? 600 : 400;
      return this.fonts[`Source Sans Pro-${sourceWeight}`] || this.fonts['Source Sans Pro'] ||
             (isBold ? this.fonts.helveticaBold : this.fonts.helvetica);
    } else if (familyLower.includes('great vibes')) {
      return this.fonts['Great Vibes-400'] || this.fonts['Great Vibes'] || this.fonts.timesRoman;
    } else if (familyLower.includes('times')) {
      return isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman;
    } else if (familyLower.includes('courier')) {
      return isBold ? this.fonts.courierBold : this.fonts.courier;
    } else {
      return isBold ? this.fonts.helveticaBold : this.fonts.helvetica;
    }
  }

  /**
   * 将十六进制颜色转换为RGB
   */
  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return rgb(0, 0, 0);
    }
    return rgb(
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    );
  }

  /**
   * 获取边框宽度
   */
  private getBorderWidth(): number {
    const borderStyle = this.template.style.border;
    if (borderStyle.includes('3pt')) return 3;
    if (borderStyle.includes('2pt')) return 2;
    return 1;
  }

  /**
   * 文本换行处理
   */
  private wrapText(text: string, font: PDFFont, fontSize: number, maxWidth: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = font.widthOfTextAtSize(testLine, fontSize);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // 单词太长，强制换行
          lines.push(word);
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }




}

/**
 * 便捷函数：生成PDF并下载
 */
export async function generateCertificatePDF(
  template: CertificateTemplate,
  data: CertificateData
): Promise<void> {
  const generator = new PDFGenerator(template, data);
  await generator.generateAndDownload();
}

/**
 * 便捷函数：生成PDF字节数组
 */
export async function generateCertificatePDFBytes(
  template: CertificateTemplate,
  data: CertificateData
): Promise<Uint8Array> {
  const generator = new PDFGenerator(template, data);
  return await generator.generate();
}
