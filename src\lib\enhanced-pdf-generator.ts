/**
 * 增强的PDF生成器
 * 基于Google Fonts管理器的完整PDF生成解决方案
 */

import { PDFDocument, PDFPage, rgb, degrees } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { GoogleFontsManager } from './google-fonts-manager';
import { ImprovedFontEmbedder, ImprovedFontEmbedResult } from './improved-font-embedder';

export class EnhancedPDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc: PDFDocument | null = null;
  private page: PDFPage | null = null;
  private googleFontsManager: GoogleFontsManager;
  private fontEmbedder: ImprovedFontEmbedder | null = null;

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
    this.googleFontsManager = GoogleFontsManager.getInstance();
  }

  /**
   * 生成PDF
   */
  async generate(): Promise<Uint8Array> {
    console.log('🚀 Starting enhanced PDF generation...');
    
    try {
      // 1. 初始化PDF文档
      await this.initializePDF();
      
      // 2. 预加载字体
      await this.preloadFonts();
      
      // 3. 创建页面
      this.createPage();
      
      // 4. 绘制内容
      await this.drawContent();
      
      // 5. 生成PDF字节
      const pdfBytes = await this.pdfDoc!.save();
      
      console.log(`✅ PDF generated successfully: ${pdfBytes.length} bytes`);
      this.logFontStatus();
      this.logEmbedStats();

      return pdfBytes;
      
    } catch (error) {
      console.error('❌ PDF generation failed:', error);
      throw error;
    }
  }

  /**
   * 初始化PDF文档
   */
  private async initializePDF(): Promise<void> {
    console.log('📄 Initializing PDF document...');
    
    this.pdfDoc = await PDFDocument.create();
    this.pdfDoc.registerFontkit(fontkit);
    
    // 初始化改进的字体嵌入器
    this.fontEmbedder = new ImprovedFontEmbedder(this.pdfDoc);
    
    console.log('✅ PDF document initialized');
  }

  /**
   * 预加载字体
   */
  private async preloadFonts(): Promise<void> {
    console.log('🔤 Preloading fonts...');

    // 收集文档中使用的所有文本
    const allTexts = [
      this.data.recipientName,
      this.data.details,
      this.data.date,
      this.data.signature
    ].filter(text => text && text.trim());

    // 让字体嵌入器收集使用的字符
    if (this.fontEmbedder) {
      this.fontEmbedder.collectUsedCharacters(allTexts);
    }

    // 收集模板中使用的字体
    const fontsToLoad = [
      { family: this.template.layout.name.fontFamily, weight: this.template.layout.name.fontWeight || 600 },
      { family: this.template.layout.details.fontFamily, weight: this.template.layout.details.fontWeight || 400 },
      { family: this.template.layout.date.fontFamily, weight: this.template.layout.date.fontWeight || 400 },
      { family: this.template.layout.signature.fontFamily, weight: this.template.layout.signature.fontWeight || 400 },
    ];

    // 去重
    const uniqueFonts = fontsToLoad.filter((font, index, self) =>
      index === self.findIndex(f => f.family === font.family && f.weight === font.weight)
    );

    console.log(`📋 Fonts to load: ${uniqueFonts.map(f => `${f.family} ${f.weight}`).join(', ')}`);

    // 预加载到Google Fonts管理器
    const loadPromises = uniqueFonts.map(async ({ family, weight }) => {
      try {
        await this.googleFontsManager.loadFont(family, weight);
        console.log(`✅ Preloaded: ${family} ${weight}`);
      } catch (error) {
        console.warn(`⚠️ Failed to preload: ${family} ${weight}`, error);
      }
    });

    await Promise.all(loadPromises);
    console.log('🎉 Font preloading completed');
  }

  /**
   * 创建页面
   */
  private createPage(): void {
    if (!this.pdfDoc) throw new Error('PDF document not initialized');

    console.log('📄 Creating page...');
    
    const [width, height] = this.getPageSize();
    this.page = this.pdfDoc.addPage([width, height]);
    
    console.log(`✅ Page created: ${width} x ${height}`);
  }

  /**
   * 获取页面尺寸
   */
  private getPageSize(): [number, number] {
    const A4_WIDTH = 595.28;
    const A4_HEIGHT = 841.89;

    if (this.template.orientation === 'landscape') {
      return [A4_HEIGHT, A4_WIDTH]; // 841.89 x 595.28
    } else {
      return [A4_WIDTH, A4_HEIGHT]; // 595.28 x 841.89
    }
  }

  /**
   * 绘制内容
   */
  private async drawContent(): Promise<void> {
    if (!this.page) throw new Error('Page not created');

    console.log('🎨 Drawing content...');

    // 绘制背景
    await this.drawBackground();

    // 绘制装饰
    this.drawDecorations();

    // 绘制文本
    await this.drawTexts();

    console.log('✅ Content drawing completed');
  }

  /**
   * 绘制背景
   */
  private async drawBackground(): Promise<void> {
    if (!this.page) return;

    const { width, height } = this.page.getSize();

    // 绘制背景色
    if (this.template.background.color && this.template.background.color !== '#ffffff') {
      const bgColor = this.hexToRgb(this.template.background.color);
      this.page.drawRectangle({
        x: 0,
        y: 0,
        width,
        height,
        color: bgColor,
      });
    }

    // 绘制边框
    if (this.template.border.width > 0) {
      const borderColor = this.hexToRgb(this.template.border.color);
      const borderWidth = this.template.border.width;

      this.page.drawRectangle({
        x: borderWidth / 2,
        y: borderWidth / 2,
        width: width - borderWidth,
        height: height - borderWidth,
        borderColor: borderColor,
        borderWidth: borderWidth,
      });
    }
  }

  /**
   * 绘制装饰
   */
  private drawDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();

    // 绘制装饰线条
    if (this.template.decorations?.lines) {
      this.template.decorations.lines.forEach(line => {
        this.page!.drawLine({
          start: { x: line.x1, y: height - line.y1 },
          end: { x: line.x2, y: height - line.y2 },
          thickness: line.thickness,
          color: this.hexToRgb(line.color),
        });
      });
    }

    // 绘制装饰圆形
    if (this.template.decorations?.circles) {
      this.template.decorations.circles.forEach(circle => {
        this.page!.drawCircle({
          x: circle.x,
          y: height - circle.y,
          size: circle.radius,
          color: this.hexToRgb(circle.color),
          opacity: circle.opacity || 1,
        });
      });
    }
  }

  /**
   * 绘制文本
   */
  private async drawTexts(): Promise<void> {
    if (!this.page) return;

    console.log('📝 Drawing texts...');

    // 绘制收件人姓名
    await this.drawText('name', this.data.recipientName);

    // 绘制详细信息
    await this.drawText('details', this.data.details);

    // 绘制日期
    await this.drawText('date', this.data.date);

    // 绘制签名
    await this.drawText('signature', this.data.signature);

    console.log('✅ Text drawing completed');
  }

  /**
   * 绘制单个文本字段
   */
  private async drawText(fieldName: keyof CertificateTemplate['layout'], text: string): Promise<void> {
    if (!this.page || !this.fontEmbedder || !text?.trim()) return;

    const layout = this.template.layout[fieldName];
    const { height } = this.page.getSize();

    console.log(`📝 Drawing ${fieldName}: "${text}"`);

    try {
      // 获取字体
      const fontResult = await this.fontEmbedder.embedFont(
        layout.fontFamily, 
        layout.fontWeight || 400
      );

      // 计算位置
      let x = layout.x;
      const y = height - layout.y;
      const fontSize = layout.fontSize;
      const color = this.hexToRgb(layout.color);

      // 处理文本对齐
      if (layout.align === 'center') {
        const textWidth = fontResult.font.widthOfTextAtSize(text, fontSize);
        x = layout.x - textWidth / 2;
      } else if (layout.align === 'right') {
        const textWidth = fontResult.font.widthOfTextAtSize(text, fontSize);
        x = layout.x - textWidth;
      }

      // 绘制文本
      this.page.drawText(text, {
        x,
        y,
        size: fontSize,
        font: fontResult.font,
        color,
      });

      console.log(`✅ ${fieldName} drawn successfully (${fontResult.isCustom ? 'custom' : 'standard'} font)`);

    } catch (error) {
      console.error(`❌ Error drawing ${fieldName}:`, error);
      throw error;
    }
  }

  /**
   * 十六进制颜色转RGB
   */
  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) return rgb(0, 0, 0);
    
    return rgb(
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    );
  }

  /**
   * 记录字体状态
   */
  private logFontStatus(): void {
    const cacheStatus = this.googleFontsManager.getCacheStatus();
    console.log(`📊 Font cache status: ${cacheStatus.size} fonts loaded`);
    console.log(`📋 Cached fonts: ${cacheStatus.fonts.join(', ')}`);
  }

  /**
   * 记录字体嵌入统计信息
   */
  private logEmbedStats(): void {
    if (this.fontEmbedder) {
      const stats = this.fontEmbedder.getEmbedStats();
      console.log(`📈 Font embed statistics:`);
      console.log(`   Total fonts embedded: ${stats.total}`);
      console.log(`   Custom fonts: ${stats.custom}`);
      console.log(`   Standard fonts: ${stats.standard}`);
      console.log(`   Fallback used: ${stats.fallback}`);
    }
  }
}
