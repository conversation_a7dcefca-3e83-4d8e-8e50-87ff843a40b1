/**
 * 下载Dancing Script字体的所有权重
 * 从Google Fonts API获取字体文件
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Dancing Script字体的权重配置
const FONT_WEIGHTS = [
  { weight: 400, name: 'Regular' },
  { weight: 500, name: 'Medium' },
  { weight: 600, name: 'SemiBold' },
  { weight: 700, name: 'Bold' }
];

// 目标目录
const FONT_DIR = path.join(__dirname, '..', 'public', 'fonts', 'Dancing_Script');

/**
 * 确保目录存在
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created directory: ${dirPath}`);
  }
}

/**
 * 下载文件
 */
function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filePath, () => {}); // 删除不完整的文件
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * 获取Google Fonts CSS并提取字体URL
 */
async function getFontUrlFromCSS(weight) {
  return new Promise((resolve, reject) => {
    const cssUrl = `https://fonts.googleapis.com/css2?family=Dancing+Script:wght@${weight}&display=swap`;
    
    https.get(cssUrl, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        // 提取TTF字体URL
        const urlMatch = data.match(/url\(([^)]+\.ttf)\)/);
        if (urlMatch) {
          resolve(urlMatch[1]);
        } else {
          reject(new Error(`No TTF URL found in CSS for weight ${weight}`));
        }
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始下载Dancing Script字体...');
  
  // 确保目录存在
  ensureDirectoryExists(FONT_DIR);
  
  for (const { weight, name } of FONT_WEIGHTS) {
    try {
      console.log(`📥 下载 Dancing Script ${name} (${weight})...`);
      
      // 获取字体URL
      const fontUrl = await getFontUrlFromCSS(weight);
      console.log(`🔗 字体URL: ${fontUrl.substring(0, 80)}...`);
      
      // 下载字体文件
      const fileName = `DancingScript-${name}.ttf`;
      const filePath = path.join(FONT_DIR, fileName);
      
      await downloadFile(fontUrl, filePath);
      
      // 检查文件大小
      const stats = fs.statSync(filePath);
      console.log(`✅ ${fileName} 下载完成 (${Math.round(stats.size / 1024)} KB)`);
      
    } catch (error) {
      console.error(`❌ 下载 ${name} 失败:`, error.message);
    }
  }
  
  console.log('🎉 Dancing Script字体下载完成！');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
