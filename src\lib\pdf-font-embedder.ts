/**
 * PDF 字体嵌入器
 * 专门处理PDF中的字体嵌入，确保字体正确显示
 */

import { PDFDocument, PDFFont, StandardFonts } from 'pdf-lib';
import { GoogleFontsManager } from './google-fonts-manager';

export interface EmbedOptions {
  subset?: boolean;
  embedStandardFonts?: boolean;
}

export interface FontEmbedResult {
  font: PDFFont;
  family: string;
  weight: number;
  isCustom: boolean;
  fallbackUsed: boolean;
}

/**
 * PDF字体嵌入器类
 */
export class PDFFontEmbedder {
  private pdfDoc: PDFDocument;
  private googleFontsManager: GoogleFontsManager;
  private embeddedFonts = new Map<string, FontEmbedResult>();

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
    this.googleFontsManager = GoogleFontsManager.getInstance();
  }

  /**
   * 嵌入字体到PDF
   */
  async embedFont(family: string, weight: number = 400): Promise<FontEmbedResult> {
    const cacheKey = `${family}-${weight}`;

    // 检查是否已嵌入
    if (this.embeddedFonts.has(cacheKey)) {
      console.log(`✅ Font already embedded: ${cacheKey}`);
      return this.embeddedFonts.get(cacheKey)!;
    }

    console.log(`🔤 Embedding font: ${family} ${weight}`);

    try {
      // 1. 尝试嵌入自定义字体
      const customResult = await this.embedCustomFont(family, weight);
      if (customResult) {
        this.embeddedFonts.set(cacheKey, customResult);
        return customResult;
      }

      // 2. 使用标准字体作为后备
      const fallbackResult = await this.embedFallbackFont(family, weight);
      this.embeddedFonts.set(cacheKey, fallbackResult);
      return fallbackResult;

    } catch (error) {
      console.error(`❌ Error embedding font ${family} ${weight}:`, error);
      
      // 最终后备：Helvetica
      const helveticaResult = await this.embedStandardFont('Helvetica', weight >= 600);
      this.embeddedFonts.set(cacheKey, helveticaResult);
      return helveticaResult;
    }
  }

  /**
   * 嵌入自定义字体
   */
  private async embedCustomFont(family: string, weight: number): Promise<FontEmbedResult | null> {
    try {
      // 从Google Fonts获取字体数据
      const fontData = await this.googleFontsManager.getFontDataForPDF(family, weight);
      if (!fontData) {
        console.warn(`⚠️ No font data available for ${family} ${weight}`);
        return null;
      }

      console.log(`📦 Embedding custom font: ${family} ${weight} (${fontData.byteLength} bytes)`);

      // 尝试多种嵌入策略
      const strategies: EmbedOptions[] = [
        { subset: false }, // 首选：不使用子集
        { subset: true },  // 备选：使用子集
        {}                 // 最后：默认选项
      ];

      for (const options of strategies) {
        try {
          const font = await this.pdfDoc.embedFont(fontData, options);
          
          console.log(`✅ Custom font embedded successfully: ${family} ${weight}`);
          console.log(`   Font name: ${font.name}`);
          console.log(`   Options used: ${JSON.stringify(options)}`);

          return {
            font,
            family,
            weight,
            isCustom: true,
            fallbackUsed: false
          };

        } catch (embedError) {
          console.warn(`⚠️ Embed strategy failed for ${family} ${weight}:`, embedError);
          continue;
        }
      }

      console.error(`❌ All embed strategies failed for ${family} ${weight}`);
      return null;

    } catch (error) {
      console.error(`❌ Error in embedCustomFont for ${family} ${weight}:`, error);
      return null;
    }
  }

  /**
   * 嵌入后备字体
   */
  private async embedFallbackFont(family: string, weight: number): Promise<FontEmbedResult> {
    console.log(`🔄 Using fallback font for ${family} ${weight}`);

    const fallbackMapping = this.getFallbackFont(family, weight);
    return await this.embedStandardFont(fallbackMapping.font, fallbackMapping.isBold);
  }

  /**
   * 嵌入标准字体
   */
  private async embedStandardFont(fontName: string, isBold: boolean = false): Promise<FontEmbedResult> {
    try {
      let standardFont: any;
      let actualFamily: string;

      switch (fontName.toLowerCase()) {
        case 'helvetica':
          standardFont = isBold ? StandardFonts.HelveticaBold : StandardFonts.Helvetica;
          actualFamily = isBold ? 'Helvetica-Bold' : 'Helvetica';
          break;
        case 'times':
        case 'times-roman':
          standardFont = isBold ? StandardFonts.TimesRomanBold : StandardFonts.TimesRoman;
          actualFamily = isBold ? 'Times-Bold' : 'Times-Roman';
          break;
        case 'courier':
          standardFont = isBold ? StandardFonts.CourierBold : StandardFonts.Courier;
          actualFamily = isBold ? 'Courier-Bold' : 'Courier';
          break;
        default:
          standardFont = StandardFonts.Helvetica;
          actualFamily = 'Helvetica';
      }

      const font = await this.pdfDoc.embedFont(standardFont);
      
      console.log(`✅ Standard font embedded: ${actualFamily}`);

      return {
        font,
        family: actualFamily,
        weight: isBold ? 700 : 400,
        isCustom: false,
        fallbackUsed: true
      };

    } catch (error) {
      console.error(`❌ Error embedding standard font ${fontName}:`, error);
      throw error;
    }
  }

  /**
   * 获取后备字体映射
   */
  private getFallbackFont(family: string, weight: number): { font: string; isBold: boolean } {
    const isBold = weight >= 600;
    const familyLower = family.toLowerCase();

    // 手写字体 -> Times Roman (更优雅)
    if (familyLower.includes('dancing') || 
        familyLower.includes('script') || 
        familyLower.includes('vibes') ||
        familyLower.includes('handwriting')) {
      return { font: 'times-roman', isBold };
    }

    // 衬线字体 -> Times Roman
    if (familyLower.includes('serif') || 
        familyLower.includes('playfair') || 
        familyLower.includes('crimson') ||
        familyLower.includes('times')) {
      return { font: 'times-roman', isBold };
    }

    // 等宽字体 -> Courier
    if (familyLower.includes('mono') || 
        familyLower.includes('courier') ||
        familyLower.includes('code')) {
      return { font: 'courier', isBold };
    }

    // 默认：无衬线字体 -> Helvetica
    return { font: 'helvetica', isBold };
  }

  /**
   * 获取已嵌入的字体
   */
  getEmbeddedFont(family: string, weight: number = 400): FontEmbedResult | null {
    const cacheKey = `${family}-${weight}`;
    return this.embeddedFonts.get(cacheKey) || null;
  }

  /**
   * 预嵌入常用字体
   */
  async preEmbedCommonFonts(): Promise<void> {
    console.log('🚀 Pre-embedding common fonts...');

    const commonFonts = [
      { family: 'Dancing Script', weight: 400 },
      { family: 'Playfair Display', weight: 400 },
      { family: 'Playfair Display', weight: 600 },
      { family: 'Inter', weight: 400 },
      { family: 'Inter', weight: 600 },
    ];

    const embedPromises = commonFonts.map(async ({ family, weight }) => {
      try {
        await this.embedFont(family, weight);
        console.log(`✅ Pre-embedded: ${family} ${weight}`);
      } catch (error) {
        console.warn(`⚠️ Failed to pre-embed: ${family} ${weight}`, error);
      }
    });

    await Promise.all(embedPromises);
    console.log('🎉 Common fonts pre-embedding completed');
  }

  /**
   * 获取嵌入状态
   */
  getEmbedStatus(): {
    totalEmbedded: number;
    customFonts: number;
    fallbackFonts: number;
    fonts: Array<{
      key: string;
      family: string;
      weight: number;
      isCustom: boolean;
      fallbackUsed: boolean;
    }>;
  } {
    const fonts = Array.from(this.embeddedFonts.entries()).map(([key, result]) => ({
      key,
      family: result.family,
      weight: result.weight,
      isCustom: result.isCustom,
      fallbackUsed: result.fallbackUsed
    }));

    return {
      totalEmbedded: this.embeddedFonts.size,
      customFonts: fonts.filter(f => f.isCustom).length,
      fallbackFonts: fonts.filter(f => f.fallbackUsed).length,
      fonts
    };
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.embeddedFonts.clear();
    console.log('🧹 PDF font embedder cache cleared');
  }
}
