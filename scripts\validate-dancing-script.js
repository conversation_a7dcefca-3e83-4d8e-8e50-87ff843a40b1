/**
 * Dancing Script字体文件验证工具
 * 验证TTF文件的完整性和字体特性
 */

const fs = require('fs');
const path = require('path');

// 字体文件路径
const FONT_DIR = path.join(__dirname, '..', 'public', 'fonts', 'Dancing_Script');

/**
 * 验证TTF文件头
 */
function validateTTFHeader(buffer) {
  const view = new DataView(buffer);
  
  // TTF文件的魔数
  const signature = view.getUint32(0, false);
  
  // 有效的TTF签名
  const validSignatures = [
    0x00010000, // TTF
    0x74727565, // 'true' (TrueType)
    0x4F54544F  // 'OTTO' (OpenType with CFF)
  ];
  
  return {
    isValid: validSignatures.includes(signature),
    signature: signature.toString(16).padStart(8, '0').toUpperCase(),
    type: signature === 0x00010000 ? 'TTF' : 
          signature === 0x74727565 ? 'TrueType' :
          signature === 0x4F54544F ? 'OpenType' : 'Unknown'
  };
}

/**
 * 读取TTF表目录
 */
function readTTFTables(buffer) {
  const view = new DataView(buffer);
  
  // 跳过版本号(4字节)
  const numTables = view.getUint16(4, false);
  
  const tables = {};
  let offset = 12; // 表目录从第12字节开始
  
  for (let i = 0; i < numTables; i++) {
    // 读取表标签 (4字节)
    const tag = String.fromCharCode(
      view.getUint8(offset),
      view.getUint8(offset + 1),
      view.getUint8(offset + 2),
      view.getUint8(offset + 3)
    );
    
    // 跳过校验和(4字节)
    const tableOffset = view.getUint32(offset + 8, false);
    const tableLength = view.getUint32(offset + 12, false);
    
    tables[tag] = {
      offset: tableOffset,
      length: tableLength
    };
    
    offset += 16; // 每个表目录条目16字节
  }
  
  return tables;
}

/**
 * 读取name表获取字体名称
 */
function readFontName(buffer, tables) {
  if (!tables['name']) {
    return 'Unknown';
  }
  
  const view = new DataView(buffer);
  const nameTableOffset = tables['name'].offset;
  
  // name表格式
  const format = view.getUint16(nameTableOffset, false);
  const count = view.getUint16(nameTableOffset + 2, false);
  const stringOffset = view.getUint16(nameTableOffset + 4, false);
  
  // 查找字体全名 (nameID = 4)
  for (let i = 0; i < count; i++) {
    const recordOffset = nameTableOffset + 6 + (i * 12);
    
    const platformID = view.getUint16(recordOffset, false);
    const encodingID = view.getUint16(recordOffset + 2, false);
    const languageID = view.getUint16(recordOffset + 4, false);
    const nameID = view.getUint16(recordOffset + 6, false);
    const length = view.getUint16(recordOffset + 8, false);
    const offset = view.getUint16(recordOffset + 10, false);
    
    // 查找字体全名 (nameID = 4) 且为英文 (platformID = 3, languageID = 0x409)
    if (nameID === 4 && platformID === 3 && languageID === 0x409) {
      const nameOffset = nameTableOffset + stringOffset + offset;
      let fontName = '';
      
      // UTF-16BE编码
      for (let j = 0; j < length; j += 2) {
        const charCode = view.getUint16(nameOffset + j, false);
        fontName += String.fromCharCode(charCode);
      }
      
      return fontName;
    }
  }
  
  return 'Unknown';
}

/**
 * 验证单个字体文件
 */
async function validateFontFile(filePath) {
  try {
    console.log(`\n🔍 验证字体文件: ${path.basename(filePath)}`);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.log('❌ 文件不存在');
      return false;
    }
    
    // 读取文件
    const buffer = fs.readFileSync(filePath);
    console.log(`📁 文件大小: ${Math.round(buffer.length / 1024)} KB`);
    
    // 验证TTF头
    const headerValidation = validateTTFHeader(buffer.buffer);
    console.log(`🔖 文件类型: ${headerValidation.type} (0x${headerValidation.signature})`);
    
    if (!headerValidation.isValid) {
      console.log('❌ 无效的TTF文件头');
      return false;
    }
    
    // 读取表目录
    const tables = readTTFTables(buffer.buffer);
    console.log(`📋 字体表数量: ${Object.keys(tables).length}`);
    console.log(`📋 包含的表: ${Object.keys(tables).join(', ')}`);
    
    // 检查必需的表
    const requiredTables = ['cmap', 'glyf', 'head', 'hhea', 'hmtx', 'loca', 'maxp', 'name', 'post'];
    const missingTables = requiredTables.filter(table => !tables[table]);
    
    if (missingTables.length > 0) {
      console.log(`⚠️ 缺少必需的表: ${missingTables.join(', ')}`);
    } else {
      console.log('✅ 所有必需的表都存在');
    }
    
    // 读取字体名称
    const fontName = readFontName(buffer.buffer, tables);
    console.log(`🏷️ 字体名称: ${fontName}`);
    
    // 检查是否为Dancing Script
    if (!fontName.toLowerCase().includes('dancing script')) {
      console.log('⚠️ 字体名称不包含"Dancing Script"');
    } else {
      console.log('✅ 字体名称验证通过');
    }
    
    console.log('✅ 字体文件验证通过');
    return true;
    
  } catch (error) {
    console.log(`❌ 验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始Dancing Script字体文件验证...');
  
  const fontFiles = [
    'DancingScript-Regular.ttf',
    'DancingScript-Medium.ttf',
    'DancingScript-SemiBold.ttf',
    'DancingScript-Bold.ttf'
  ];
  
  let validCount = 0;
  
  for (const fileName of fontFiles) {
    const filePath = path.join(FONT_DIR, fileName);
    const isValid = await validateFontFile(filePath);
    if (isValid) validCount++;
  }
  
  console.log(`\n🎉 验证完成: ${validCount}/${fontFiles.length} 个字体文件有效`);
  
  if (validCount === fontFiles.length) {
    console.log('✅ 所有Dancing Script字体文件验证通过！');
  } else {
    console.log('⚠️ 部分字体文件存在问题，请检查');
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { validateFontFile, main };
